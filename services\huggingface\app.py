"""
HuggingFace Text-to-Image Service

FastAPI service that dynamically loads and runs HuggingFace diffusion models.
Supports any diffusers-compatible model from the HuggingFace Hub.
"""

import asyncio
import logging
import logging.config
import os
import base64
import io
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional, List

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel, Field
from PIL import Image

import config
from model_manager import ModelManager

# Configure logging
logging.config.dictConfig(config.LOGGING_CONFIG)
logger = logging.getLogger(__name__)

# Global model manager
model_manager: Optional[ModelManager] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global model_manager
    
    # Startup
    logger.info("Starting HuggingFace Text-to-Image Service")
    logger.info(f"Device: {config.DEVICE}")
    logger.info(f"Cache directory: {config.HF_CACHE_DIR}")
    
    # Create directories
    os.makedirs(config.TEMP_DIR, exist_ok=True)
    os.makedirs(config.LOG_DIR, exist_ok=True)
    
    # Initialize model manager
    model_manager = ModelManager()
    
    yield
    
    # Shutdown
    logger.info("Shutting down HuggingFace Text-to-Image Service")

# Create FastAPI app
app = FastAPI(
    title="HuggingFace Text-to-Image Service",
    description="Dynamic text-to-image generation using HuggingFace diffusion models",
    version="1.0.0",
    lifespan=lifespan
)

# Request/Response Models
class ModelLoadRequest(BaseModel):
    modelId: str = Field(..., description="HuggingFace model ID or alias")
    force: bool = Field(False, description="Force reload if already loaded")
    precision: str = Field("fp16", description="Model precision (fp16 or fp32)")
    device: str = Field("auto", description="Device to load model on (cuda, cpu, auto)")

class ModelLoadResponse(BaseModel):
    success: bool
    modelId: str
    loadTime: int  # milliseconds
    memoryUsage: int  # MB
    error: Optional[str] = None

class GenerationRequest(BaseModel):
    modelId: str = Field(..., description="HuggingFace model ID or alias")
    prompt: str = Field(..., description="Text prompt for image generation")
    negativePrompt: Optional[str] = Field(None, description="Negative prompt")
    width: int = Field(512, description="Image width")
    height: int = Field(512, description="Image height")
    numInferenceSteps: int = Field(20, description="Number of inference steps")
    guidanceScale: float = Field(7.5, description="Guidance scale")
    seed: Optional[int] = Field(None, description="Random seed")
    scheduler: Optional[str] = Field(None, description="Scheduler name")

class GenerationResponse(BaseModel):
    success: bool
    imageBase64: Optional[str] = None
    metadata: Dict[str, Any]
    error: Optional[str] = None

class ModelInfo(BaseModel):
    modelId: str
    loaded: bool
    memoryUsage: Optional[int] = None
    loadTime: Optional[float] = None
    capabilities: List[str]
    parameters: Dict[str, Any]

class HealthResponse(BaseModel):
    status: str
    loadedModels: List[str]
    memoryUsage: Dict[str, Any]
    gpuInfo: Optional[Dict[str, Any]] = None

# API Endpoints
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        loaded_models = [info["modelId"] for info in model_manager.list_loaded_models()]
        memory_info = model_manager.get_memory_info()
        
        return HealthResponse(
            status="healthy",
            loadedModels=loaded_models,
            memoryUsage=memory_info,
            gpuInfo=memory_info.get("gpu")
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="unhealthy",
            loadedModels=[],
            memoryUsage={},
            error=str(e)
        )

@app.post("/models/load", response_model=ModelLoadResponse)
async def load_model(request: ModelLoadRequest):
    """Load a model dynamically from HuggingFace Hub"""
    try:
        result = await model_manager.load_model(
            model_id=request.modelId,
            force=request.force,
            precision=request.precision,
            device=request.device if request.device != "auto" else config.DEVICE
        )
        
        return ModelLoadResponse(**result)
        
    except Exception as e:
        logger.error(f"Model loading failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/models/{model_id}", response_model=ModelInfo)
async def get_model_info(model_id: str):
    """Get information about a loaded model"""
    try:
        info = model_manager.get_model_info(model_id)
        if not info:
            raise HTTPException(status_code=404, detail=f"Model {model_id} not loaded")
        
        return ModelInfo(**info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get model info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/models", response_model=List[ModelInfo])
async def list_models():
    """List all loaded models"""
    try:
        models = model_manager.list_loaded_models()
        return [ModelInfo(**model) for model in models]
        
    except Exception as e:
        logger.error(f"Failed to list models: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/models/{model_id}")
async def unload_model(model_id: str):
    """Unload a specific model"""
    try:
        result = await model_manager.unload_model(model_id)
        return result

    except Exception as e:
        logger.error(f"Failed to unload model: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate", response_model=GenerationResponse)
async def generate_image(request: GenerationRequest):
    """Generate an image from text"""
    try:
        # Ensure model is loaded
        model_info = model_manager.get_model_info(request.modelId)
        if not model_info:
            # Try to load the model automatically
            logger.info(f"Auto-loading model: {request.modelId}")
            load_result = await model_manager.load_model(request.modelId)
            if not load_result["success"]:
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to load model {request.modelId}: {load_result.get('error')}"
                )

        # Generate image
        import torch
        generation_params = {
            "width": request.width,
            "height": request.height,
            "num_inference_steps": request.numInferenceSteps,
            "guidance_scale": request.guidanceScale,
            "generator": None if request.seed is None else torch.Generator().manual_seed(request.seed)
        }

        # Remove None values
        generation_params = {k: v for k, v in generation_params.items() if v is not None}

        result = await model_manager.generate_image(
            model_id=request.modelId,
            prompt=request.prompt,
            negative_prompt=request.negativePrompt,
            **generation_params
        )

        # Convert PIL Image to base64
        image = result["image"]
        buffer = io.BytesIO()
        image.save(buffer, format="PNG")
        image_base64 = base64.b64encode(buffer.getvalue()).decode()

        return GenerationResponse(
            success=True,
            imageBase64=image_base64,
            metadata={
                "modelId": request.modelId,
                "generationTime": result["generation_time"],
                "parameters": result["parameters"],
                "seed": request.seed
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Image generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    # Import torch here to avoid issues with multiprocessing
    import torch

    uvicorn.run(
        "app:app",
        host=config.SERVER_HOST,
        port=config.SERVER_PORT,
        log_level="info" if not config.DEBUG else "debug",
        reload=config.DEBUG
    )
