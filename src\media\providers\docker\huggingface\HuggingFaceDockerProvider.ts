/**
 * HuggingFaceDockerProvider
 * 
 * Provider implementation for HuggingFace text-to-image models running in Docker containers.
 * Supports dynamic loading of any diffusers-compatible model from HuggingFace Hub.
 */

import { 
  MediaProvider,
  ProviderType,
  MediaCapability,
  ProviderModel,
  ProviderConfig
} from '../../../types/provider';
import { HuggingFaceDockerService } from '../../../services/HuggingFaceDockerService';
import { HuggingFaceAPIClient } from './HuggingFaceAPIClient';
import { HuggingFaceDockerModel } from './HuggingFaceDockerModel';
import { TextToImageModel } from '../../../models/abstracts/TextToImageModel';
import { TextToImageProvider } from '../../../capabilities';

/**
 * Provider for HuggingFace text-to-image models via Docker
 */
export class HuggingFaceDockerProvider implements MediaProvider, TextToImageProvider {
  readonly id = 'huggingface-docker';
  readonly name = 'HuggingFace Docker Provider';
  readonly type = ProviderType.LOCAL;
  readonly capabilities = [MediaCapability.TEXT_TO_IMAGE];
  readonly models: ProviderModel[] = [];

  private dockerService?: HuggingFaceDockerService;
  private apiClient?: HuggingFaceAPIClient;
  private config?: ProviderConfig;

  /**
   * Get the Docker service instance
   */
  protected async getDockerService(): Promise<HuggingFaceDockerService> {
    if (!this.dockerService) {
      this.dockerService = new HuggingFaceDockerService();
    }
    return this.dockerService;
  }

  /**
   * Get the API client instance
   */
  protected async getAPIClient(): Promise<HuggingFaceAPIClient> {
    if (!this.apiClient) {
      this.apiClient = new HuggingFaceAPIClient();
    }
    return this.apiClient;
  }

  /**
   * Start the Docker service
   */
  async startService(): Promise<boolean> {
    try {
      const dockerService = await this.getDockerService();
      const started = await dockerService.startService();
      
      if (started) {
        // Wait for service to be healthy
        const healthy = await dockerService.waitForHealthy(120000); // 2 minutes
        return healthy;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to start HuggingFace Docker service:', error);
      return false;
    }
  }

  /**
   * Stop the Docker service
   */
  async stopService(): Promise<boolean> {
    try {
      const dockerService = await this.getDockerService();
      return await dockerService.stopService();
    } catch (error) {
      console.error('Failed to stop HuggingFace Docker service:', error);
      return false;
    }
  }

  /**
   * Get service status
   */
  async getServiceStatus(): Promise<any> {
    try {
      const dockerService = await this.getDockerService();
      return await dockerService.getServiceStatus();
    } catch (error) {
      console.error('Failed to get HuggingFace service status:', error);
      return { running: false, healthy: false };
    }
  }

  /**
   * Get available models (dynamic - any HF model ID can be used)
   */
  getAvailableModels(): string[] {
    // Return some popular models as examples, but any HF model ID can be used
    return [
      'runwayml/stable-diffusion-v1-5',
      'stabilityai/stable-diffusion-xl-base-1.0',
      'stabilityai/stable-diffusion-2-1',
      'black-forest-labs/FLUX.1-dev',
      'black-forest-labs/FLUX.1-schnell',
      'SimianLuo/LCM_Dreamshaper_v7',
      'prompthero/openjourney-v4',
      'wavymulder/Analog-Diffusion',
      'nitrosocke/Arcane-Diffusion'
    ];
  }

  /**
   * Create a text-to-image model instance
   */
  async createTextToImageModel(modelId: string): Promise<TextToImageModel> {
    const dockerService = await this.getDockerService();
    const apiClient = await this.getAPIClient();

    // Create Docker-specific model with injected dependencies
    const model = new HuggingFaceDockerModel({
      apiClient,
      modelId,
      autoLoad: true // Enable automatic model loading
    });

    return model;
  }

  /**
   * Get supported text-to-image models (TextToImageProvider interface)
   */
  getSupportedTextToImageModels(): string[] {
    return this.getAvailableModels();
  }

  /**
   * Check if provider supports a specific text-to-image model (TextToImageProvider interface)
   */
  supportsTextToImageModel(modelId: string): boolean {
    // HuggingFace provider supports any valid HF model ID
    // We could add validation here, but for now accept any string
    return typeof modelId === 'string' && modelId.length > 0;
  }

  /**
   * Check if provider supports a specific model
   */
  supportsModel(modelId: string): boolean {
    return this.supportsTextToImageModel(modelId);
  }

  /**
   * Get provider information
   */
  getInfo() {
    return {
      description: 'Provides dynamic HuggingFace text-to-image models via Docker containers',
      dockerImage: 'huggingface-text-to-image:latest',
      defaultPort: 8007,
      capabilities: [
        'text-to-image',
        'dynamic-model-loading',
        'stable-diffusion',
        'flux',
        'custom-models'
      ],
      features: [
        'Any HuggingFace diffusers model',
        'Dynamic model loading',
        'Memory management',
        'GPU acceleration',
        'Model caching'
      ]
    };
  }

  /**
   * Configure the provider
   */
  async configure(config: ProviderConfig): Promise<void> {
    this.config = config;
    // Docker providers typically don't need API keys, but may need service URLs
    if (config.baseUrl) {
      this.apiClient = new HuggingFaceAPIClient({ baseUrl: config.baseUrl });
    }
  }

  /**
   * Check if provider is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      const status = await this.getServiceStatus();
      return status.running && status.health === 'healthy';
    } catch {
      return false;
    }
  }

  /**
   * Get models for specific capability
   */
  getModelsForCapability(capability: MediaCapability): ProviderModel[] {
    if (capability === MediaCapability.TEXT_TO_IMAGE) {
      // Return dynamic model definitions
      return this.getAvailableModels().map(modelId => ({
        id: modelId,
        name: `HuggingFace: ${modelId}`,
        description: `Dynamic HuggingFace text-to-image model: ${modelId}`,
        capabilities: [MediaCapability.TEXT_TO_IMAGE],
        parameters: {
          width: { type: 'number', default: 512, min: 64, max: 2048 },
          height: { type: 'number', default: 512, min: 64, max: 2048 },
          numInferenceSteps: { type: 'number', default: 20, min: 1, max: 100 },
          guidanceScale: { type: 'number', default: 7.5, min: 1.0, max: 20.0 },
          seed: { type: 'number', optional: true },
          negativePrompt: { type: 'string', optional: true },
          scheduler: { 
            type: 'string', 
            optional: true,
            options: ['DPMSolverMultistepScheduler', 'EulerDiscreteScheduler', 'DDIMScheduler']
          }
        },
        pricing: {
          inputCost: 0, // Free local service
          outputCost: 0,
          currency: 'USD'
        }
      }));
    }
    return [];
  }

  /**
   * Get a model instance by ID with automatic type detection
   */
  async getModel(modelId: string): Promise<TextToImageModel> {
    if (!await this.isAvailable()) {
      throw new Error('HuggingFace provider is not available');
    }

    // HuggingFace provider only supports text-to-image models
    return this.createTextToImageModel(modelId);
  }

  /**
   * Load a specific model in the service
   */
  async loadModel(modelId: string, options?: { force?: boolean; precision?: string }): Promise<any> {
    try {
      const apiClient = await this.getAPIClient();
      return await apiClient.loadModel({
        modelId,
        force: options?.force || false,
        precision: options?.precision as 'fp16' | 'fp32' || 'fp16'
      });
    } catch (error) {
      console.error(`Failed to load model ${modelId}:`, error);
      throw error;
    }
  }

  /**
   * Unload a specific model from the service
   */
  async unloadModel(modelId: string): Promise<any> {
    try {
      const apiClient = await this.getAPIClient();
      return await apiClient.unloadModel(modelId);
    } catch (error) {
      console.error(`Failed to unload model ${modelId}:`, error);
      throw error;
    }
  }

  /**
   * List currently loaded models in the service
   */
  async listLoadedModels(): Promise<any[]> {
    try {
      const apiClient = await this.getAPIClient();
      return await apiClient.listLoadedModels();
    } catch (error) {
      console.error('Failed to list loaded models:', error);
      return [];
    }
  }

  /**
   * Get service health information
   */
  async getServiceHealth(): Promise<any> {
    try {
      const apiClient = await this.getAPIClient();
      return await apiClient.healthCheck();
    } catch (error) {
      console.error('Failed to get service health:', error);
      return { status: 'unhealthy', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
}

// Self-register with the provider registry
import { ProviderRegistry } from '../../../registry/ProviderRegistry';
ProviderRegistry.getInstance().register('huggingface-docker', HuggingFaceDockerProvider);
