# Core Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Machine Learning & Image Generation
torch>=2.1.0
torchvision>=0.16.0
diffusers>=0.24.0
transformers>=4.35.0
accelerate>=0.24.0

# HuggingFace Hub
huggingface_hub>=0.19.0
safetensors>=0.4.0

# Image Processing
Pillow>=10.0.0
opencv-python-headless>=4.8.0

# Utilities
numpy>=1.24.0
requests>=2.31.0
aiofiles>=23.2.0
python-multipart>=0.0.6

# Monitoring & Logging
psutil>=5.9.0
pydantic>=2.4.0

# Optional: For better performance
xformers>=0.0.22; platform_machine == "x86_64"
