"""
Dynamic Model Manager for HuggingFace Text-to-Image Models

Handles loading, caching, and memory management of diffusers models.
"""

import asyncio
import logging
import time
import gc
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import torch
import psutil
from diffusers import (
    StableDiffusionPipeline,
    StableDiffusionXLPipeline,
    DiffusionPipeline,
    DPMSolverMultistepScheduler,
    EulerDiscreteScheduler,
    DDIMScheduler
)
from huggingface_hub import model_info, HfApi
import config

logger = logging.getLogger(__name__)

@dataclass
class ModelInfo:
    model_id: str
    pipeline: Optional[DiffusionPipeline]
    load_time: float
    last_used: float
    memory_usage: int
    device: str
    precision: str

class ModelManager:
    """Manages dynamic loading and caching of HuggingFace diffusion models"""
    
    def __init__(self):
        self.loaded_models: Dict[str, ModelInfo] = {}
        self.loading_locks: Dict[str, asyncio.Lock] = {}
        self.hf_api = HfApi()
        
    async def load_model(
        self, 
        model_id: str, 
        force: bool = False,
        precision: str = config.DEFAULT_PRECISION,
        device: str = config.DEVICE
    ) -> Dict[str, Any]:
        """Load a model dynamically from HuggingFace Hub"""
        
        resolved_id = config.resolve_model_id(model_id)
        logger.info(f"Loading model: {resolved_id}")
        
        # Check if already loaded
        if resolved_id in self.loaded_models and not force:
            model_info = self.loaded_models[resolved_id]
            model_info.last_used = time.time()
            logger.info(f"Model {resolved_id} already loaded")
            return {
                "success": True,
                "modelId": resolved_id,
                "loadTime": 0,
                "memoryUsage": model_info.memory_usage,
                "cached": True
            }
        
        # Ensure we have a lock for this model
        if resolved_id not in self.loading_locks:
            self.loading_locks[resolved_id] = asyncio.Lock()
        
        async with self.loading_locks[resolved_id]:
            # Double-check after acquiring lock
            if resolved_id in self.loaded_models and not force:
                model_info = self.loaded_models[resolved_id]
                model_info.last_used = time.time()
                return {
                    "success": True,
                    "modelId": resolved_id,
                    "loadTime": 0,
                    "memoryUsage": model_info.memory_usage,
                    "cached": True
                }
            
            try:
                # Check memory and unload models if needed
                await self._manage_memory()
                
                # Validate model exists on HuggingFace Hub
                await self._validate_model(resolved_id)
                
                start_time = time.time()
                
                # Get model-specific configuration
                model_config = config.get_model_config(resolved_id)
                
                # Load the pipeline
                logger.info(f"Downloading and loading pipeline for {resolved_id}")
                pipeline = await self._load_pipeline(resolved_id, model_config, device)
                
                # Enable memory efficient attention if available
                if config.ENABLE_MEMORY_EFFICIENT_ATTENTION and hasattr(pipeline.unet, 'set_use_memory_efficient_attention_xformers'):
                    try:
                        pipeline.unet.set_use_memory_efficient_attention_xformers(True)
                        logger.info("Enabled memory efficient attention")
                    except Exception as e:
                        logger.warning(f"Could not enable memory efficient attention: {e}")
                
                # Enable CPU offload if configured
                if config.ENABLE_CPU_OFFLOAD:
                    pipeline.enable_sequential_cpu_offload()
                    logger.info("Enabled CPU offload")
                
                load_time = time.time() - start_time
                memory_usage = self._get_model_memory_usage()
                
                # Store model info
                self.loaded_models[resolved_id] = ModelInfo(
                    model_id=resolved_id,
                    pipeline=pipeline,
                    load_time=load_time,
                    last_used=time.time(),
                    memory_usage=memory_usage,
                    device=device,
                    precision=precision
                )
                
                logger.info(f"Successfully loaded {resolved_id} in {load_time:.2f}s")
                
                return {
                    "success": True,
                    "modelId": resolved_id,
                    "loadTime": int(load_time * 1000),  # Convert to milliseconds
                    "memoryUsage": memory_usage
                }
                
            except Exception as e:
                logger.error(f"Failed to load model {resolved_id}: {str(e)}")
                return {
                    "success": False,
                    "modelId": resolved_id,
                    "error": str(e)
                }
    
    async def _load_pipeline(self, model_id: str, config_dict: Dict[str, Any], device: str) -> DiffusionPipeline:
        """Load the diffusion pipeline"""
        
        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        
        def _load():
            return DiffusionPipeline.from_pretrained(
                model_id,
                **config_dict
            ).to(device)
        
        return await loop.run_in_executor(None, _load)
    
    async def _validate_model(self, model_id: str):
        """Validate that the model exists and is compatible"""
        try:
            # Run in thread pool
            loop = asyncio.get_event_loop()
            info = await loop.run_in_executor(None, lambda: model_info(model_id))
            
            # Check if it's a diffusers model
            if 'diffusers' not in info.tags:
                raise ValueError(f"Model {model_id} is not a diffusers model")
                
        except Exception as e:
            raise ValueError(f"Model validation failed for {model_id}: {str(e)}")
    
    async def _manage_memory(self):
        """Manage memory by unloading old models if needed"""
        if len(self.loaded_models) >= config.MAX_LOADED_MODELS:
            # Find the least recently used model
            oldest_model = min(
                self.loaded_models.values(),
                key=lambda x: x.last_used
            )
            
            logger.info(f"Unloading least recently used model: {oldest_model.model_id}")
            await self.unload_model(oldest_model.model_id)
    
    def _get_model_memory_usage(self) -> int:
        """Get current memory usage in MB"""
        if torch.cuda.is_available():
            return int(torch.cuda.memory_allocated() / 1024 / 1024)
        else:
            process = psutil.Process()
            return int(process.memory_info().rss / 1024 / 1024)
    
    async def unload_model(self, model_id: str) -> Dict[str, Any]:
        """Unload a specific model"""
        resolved_id = config.resolve_model_id(model_id)
        
        if resolved_id not in self.loaded_models:
            return {"success": False, "message": f"Model {resolved_id} not loaded"}
        
        try:
            model_info = self.loaded_models[resolved_id]
            
            # Move to CPU and delete
            if model_info.pipeline:
                model_info.pipeline.to("cpu")
                del model_info.pipeline
            
            del self.loaded_models[resolved_id]
            
            # Force garbage collection
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            logger.info(f"Unloaded model: {resolved_id}")
            
            return {"success": True, "message": f"Model {resolved_id} unloaded"}
            
        except Exception as e:
            logger.error(f"Failed to unload model {resolved_id}: {str(e)}")
            return {"success": False, "message": str(e)}
    
    def get_model_info(self, model_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a loaded model"""
        resolved_id = config.resolve_model_id(model_id)
        
        if resolved_id not in self.loaded_models:
            return None
        
        model_info = self.loaded_models[resolved_id]
        return {
            "modelId": resolved_id,
            "loaded": True,
            "loadTime": model_info.load_time,
            "lastUsed": model_info.last_used,
            "memoryUsage": model_info.memory_usage,
            "device": model_info.device,
            "precision": model_info.precision,
            "capabilities": ["text-to-image"],
            "parameters": config.get_model_config(resolved_id)
        }
    
    def list_loaded_models(self) -> List[Dict[str, Any]]:
        """List all loaded models"""
        return [
            self.get_model_info(model_id) 
            for model_id in self.loaded_models.keys()
        ]
    
    async def generate_image(
        self,
        model_id: str,
        prompt: str,
        negative_prompt: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate an image using a loaded model"""
        
        resolved_id = config.resolve_model_id(model_id)
        
        if resolved_id not in self.loaded_models:
            raise ValueError(f"Model {resolved_id} not loaded")
        
        model_info = self.loaded_models[resolved_id]
        model_info.last_used = time.time()
        
        try:
            # Prepare generation parameters
            generation_params = {
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                **config.DEFAULT_GENERATION_CONFIG,
                **kwargs
            }
            
            # Remove None values
            generation_params = {k: v for k, v in generation_params.items() if v is not None}
            
            logger.info(f"Generating image with {resolved_id}: {prompt[:50]}...")
            
            start_time = time.time()
            
            # Run generation in thread pool
            loop = asyncio.get_event_loop()
            
            def _generate():
                return model_info.pipeline(**generation_params).images[0]
            
            image = await loop.run_in_executor(None, _generate)
            
            generation_time = time.time() - start_time
            
            logger.info(f"Image generated in {generation_time:.2f}s")
            
            return {
                "image": image,
                "generation_time": generation_time,
                "parameters": generation_params
            }
            
        except Exception as e:
            logger.error(f"Image generation failed: {str(e)}")
            raise
    
    def get_memory_info(self) -> Dict[str, Any]:
        """Get current memory usage information"""
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory
            gpu_allocated = torch.cuda.memory_allocated()
            gpu_cached = torch.cuda.memory_reserved()
            
            return {
                "gpu": {
                    "available": True,
                    "name": torch.cuda.get_device_name(0),
                    "total": int(gpu_memory / 1024 / 1024),  # MB
                    "allocated": int(gpu_allocated / 1024 / 1024),  # MB
                    "cached": int(gpu_cached / 1024 / 1024)  # MB
                }
            }
        else:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                "cpu": {
                    "rss": int(memory_info.rss / 1024 / 1024),  # MB
                    "vms": int(memory_info.vms / 1024 / 1024)   # MB
                }
            }
