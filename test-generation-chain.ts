/**
 * Test Generation Chain - Demonstrate how generation_prompt creates a complete lineage
 * 
 * This test demonstrates the powerful generation chain feature:
 * 1. Original Text -> LLM -> Enhanced Text (generation_prompt contains original text)
 * 2. Enhanced Text -> Image Model -> Image (generation_prompt contains the enhanced text, which has its own generation_prompt)
 * 3. Image -> Video Model -> Video (generation_prompt contains the image, which contains the text chain)
 * 
 * This creates a complete audit trail: Video -> Image -> Enhanced Text -> Original Text
 */

import { initializeProviders, getProvider } from './src/media/registry/bootstrap';
import { Text } from './src/media/assets/roles/classes/Text';

async function testGenerationChain() {
  console.log('🔗 Testing Generation Chain with generation_prompt metadata...');
  
  try {
    // Initialize providers
    await initializeProviders();
    
    console.log('\n=== STEP 1: Original Text ===');
    const originalPrompt = "A majestic dragon";
    const originalText = new Text(originalPrompt);
    console.log(`📝 Original text: "${originalPrompt}"`);
    console.log(`   Generation prompt: ${originalText.metadata?.generation_prompt ? 'Yes' : 'None (original source)'}`);
    
    console.log('\n=== STEP 2: Text -> Enhanced Text via LLM ===');
    const openrouterProvider = await getProvider('openrouter');
    const llmModel = await openrouterProvider.getModel('deepseek/deepseek-chat'); // Free model
    
    console.log(`🤖 Enhancing text with LLM: ${llmModel.getId()}`);
    const enhancedText = await llmModel.transform(originalText, {
      system: "You are a creative assistant. Enhance the given text into a detailed, vivid image description suitable for AI image generation. Keep it under 100 words.",
      temperature: 0.7
    });
    
    console.log(`📝 Enhanced text: "${enhancedText.content.substring(0, 100)}..."`);
    console.log(`   Has generation_prompt: ${enhancedText.metadata?.generation_prompt ? 'YES' : 'NO'}`);
    
    if (enhancedText.metadata?.generation_prompt) {
      console.log(`   🔗 Chain: Enhanced text was generated from:`, 
        typeof enhancedText.metadata.generation_prompt.input === 'object' ? 
        `[${enhancedText.metadata.generation_prompt.input.constructor.name} object]` : 
        enhancedText.metadata.generation_prompt.input);
    }
    
    console.log('\n=== STEP 3: Enhanced Text -> Image ===');
    const replicateProvider = await getProvider('replicate');
    const imageModel = await replicateProvider.getModel('black-forest-labs/flux-schnell');
    
    console.log(`🎨 Generating image with: ${imageModel.getId()}`);
    const startTime = Date.now();
    const image = await imageModel.transform(enhancedText, {
      aspect_ratio: "16:9",
      steps: 4,
      megapixels: "1"
    });
    const imageTime = Date.now() - startTime;
    
    console.log(`🖼️  Image generated in ${imageTime}ms`);
    console.log(`   Format: ${image.format}, Size: ${(image.data.length / 1024).toFixed(1)}KB`);
    console.log(`   Has generation_prompt: ${image.metadata?.generation_prompt ? 'YES' : 'NO'}`);
    
    if (image.metadata?.generation_prompt) {
      console.log(`   🔗 Chain: Image was generated from:`, 
        typeof image.metadata.generation_prompt.input === 'object' ? 
        `[${image.metadata.generation_prompt.input.constructor.name} object]` : 
        image.metadata.generation_prompt.input);
        
      // Check if the enhanced text has its own generation_prompt (pointing to original text)
      const enhancedTextInput = image.metadata.generation_prompt.input;
      if (enhancedTextInput?.metadata?.generation_prompt) {
        console.log(`   🔗 Chain continues: Enhanced text was generated from:`,
          typeof enhancedTextInput.metadata.generation_prompt.input === 'object' ? 
          `[${enhancedTextInput.metadata.generation_prompt.input.constructor.name} object]` : 
          enhancedTextInput.metadata.generation_prompt.input);
      }
    }
    
    console.log('\n=== GENERATION LINEAGE ANALYSIS ===');
    console.log('📊 Complete Generation Chain:');
    
    // Trace the complete lineage
    let currentAsset = image;
    let depth = 0;
    const maxDepth = 10; // Prevent infinite loops
    
    while (currentAsset?.metadata?.generation_prompt && depth < maxDepth) {
      const gp = currentAsset.metadata.generation_prompt;
      const indent = '  '.repeat(depth + 1);
      
      console.log(`${indent}📍 Step ${depth + 1}:`);
      console.log(`${indent}   Asset Type: ${currentAsset.constructor.name}`);
      console.log(`${indent}   Generated by: ${gp.provider}/${gp.modelName} (${gp.transformationType})`);
      console.log(`${indent}   Timestamp: ${gp.timestamp}`);
      console.log(`${indent}   Options: ${JSON.stringify(gp.options)}`);
      
      // Move to the input of this generation step
      currentAsset = gp.input;
      depth++;
    }
    
    if (currentAsset && depth < maxDepth) {
      const indent = '  '.repeat(depth + 1);
      console.log(`${indent}📍 Original Source:`);
      console.log(`${indent}   Asset Type: ${currentAsset.constructor.name}`);
      console.log(`${indent}   Content: "${typeof currentAsset.content === 'string' ? currentAsset.content.substring(0, 50) : '[Binary data]'}..."`);
    }
    
    console.log(`\n✅ Successfully traced ${depth + 1} levels of generation history!`);
    console.log('🎉 Generation chain test completed successfully!');
    
  } catch (error) {
    console.error('❌ Generation chain test failed:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }
}

// Run the test
testGenerationChain().catch(console.error);
